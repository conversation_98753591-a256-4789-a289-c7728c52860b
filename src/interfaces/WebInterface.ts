import express, { Request, Response } from 'express';
import cookieParser from 'cookie-parser';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { ChatService } from '../core/ChatService';
import { ForaChat } from '../operations';
import { MessageInterface, ChatResponse, SessionCreateRequest } from '../models/types';
import { logger } from '../utils/Logger';
import { v4 as uuidv4 } from 'uuid';
import { MessageQueueService } from '../core/MessageQueueService';
import { ConversationService } from '../core/ConversationService';

export class WebInterface implements MessageInterface {
  private app: express.Application;
  private chatService: ChatService;

  constructor(chatService: ChatService) {
    this.chatService = chatService;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());
    this.app.use(cookieParser());

    // Add CORS if needed
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Cookie');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Credentials', 'true');
      next();
    });

    // Session middleware - ensure user has a session
    this.app.use(this.ensureSession.bind(this));

    // Serve static files from the React build directory only
    this.app.use(express.static('public/dist'));
  }

  private async ensureSession(req: Request, res: Response, next: express.NextFunction): Promise<void> {
    try {
      // Skip session handling for health check and static files
      if (req.path === '/health' || req.path.startsWith('/dist/') || req.path.endsWith('.js') || req.path.endsWith('.css') || req.path.endsWith('.svg')) {
        next();
        return;
      }

      let sessionId = req.cookies?.forachat_session;
      let session = null;

      if (sessionId) {
        logger.debug(`Attempting to retrieve session ${sessionId} for ${req.path}`);
        // Try to get existing session
        const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
        session = await handle.getResult();

        if (!session) {
          logger.debug(`Session ${sessionId} not found or expired, will create new session`);
        } else {
          logger.debug(`Successfully retrieved session ${sessionId} for user ${session.user_identifier}`);
        }
      } else {
        logger.debug(`No session cookie found for ${req.path}, will create new session`);
      }

      if (!session) {
        // Create new session
        sessionId = uuidv4();
        const userIdentifier = req.ip || 'unknown';

        const sessionRequest: SessionCreateRequest = {
          userIdentifier: `web_${userIdentifier}`,
          channel: 'web',
          metadata: {
            userAgent: req.headers['user-agent'],
            ip: req.ip
          }
        };

        const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await handle.getResult();
        sessionId = session.id;

        // Set cookie
        res.cookie('forachat_session', sessionId, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
        });
      } else {
        // Update session activity
        await DBOS.startWorkflow(ForaChat).updateSessionActivity(sessionId);
      }

      // Add session to request for use in handlers
      (req as any).session = session;
      next();
    } catch (error) {
      logger.error(`Error in session middleware`, error);
      next(error);
    }
  }

  private setupRoutes(): void {
    // Serve the React build at root
    this.app.get('/', (req, res) => {
      res.sendFile('index.html', { root: 'public/dist' });
    });

    // Main chat endpoint
    this.app.post('/chat', this.handleChatRequest.bind(this));

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Get conversation history
    this.app.get('/conversation/:id', this.getConversationHistory.bind(this));

    // Get queued messages for a conversation (for testing)
    this.app.get('/conversation/:id/queue', this.getConversationQueue.bind(this));

    // Continue existing conversation
    this.app.post('/conversation/:id/message', this.continueConversation.bind(this));

    // Session management endpoints
    this.app.get('/session', this.getSessionInfo.bind(this));
    this.app.get('/session/conversations', this.getSessionConversations.bind(this));

    // API endpoints for REPL and other clients
    this.app.post('/api/session', this.createSessionAPI.bind(this));
    this.app.get('/api/session/:id', this.getSessionInfoAPI.bind(this));
    this.app.delete('/api/session/:id', this.deleteSessionAPI.bind(this));
    this.app.put('/api/session/:id/extend', this.extendSessionAPI.bind(this));
    this.app.get('/api/session/:id/conversation', this.getSessionConversationAPI.bind(this));
    this.app.get('/api/user/:identifier/sessions', this.getUserSessionsAPI.bind(this));
    this.app.post('/api/sessions/cleanup', this.cleanupSessionsAPI.bind(this));
    this.app.get('/api/sessions/stats', this.getCleanupStatsAPI.bind(this));
    this.app.post('/api/sessions/manual-cleanup', this.manualCleanupAPI.bind(this));
  }

  private async handleChatRequest(req: Request, res: Response): Promise<void> {
    try {
      const { text } = req.body;
      const session = (req as any).session;

      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        res.status(400).json({
          error: 'Request body must include a non-empty "text" field'
        });
        return;
      }

      // Log user request
      logger.info(`[WEB] User request: "${text.trim()}" | Session: ${session?.id || 'unknown'} | Conversation: ${session?.conversation_id || 'new'} | IP: ${req.ip}`);

      let result;
      if (session?.conversation_id) {
        // Continue existing conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text.trim(), session.conversation_id);
        result = await handle.getResult();
      } else {
        // Start new conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text.trim());
        result = await handle.getResult();

        // Update session with new conversation ID
        if (result.conversationId) {
          await DBOS.startWorkflow(ForaChat).updateSessionConversation(session.id, result.conversationId);
        }
      }

      // Start polling for queued character thoughts if no immediate messages
      if (!result.reply || result.reply.length === 0) {
        logger.info(`No immediate messages in HTTP response, starting background polling for queued character thoughts`);
        this.startBackgroundPolling(result.conversationId);
      }

      res.json(result);

    } catch (error) {
      logger.error(`Error in /chat handler`, error);
      const errorMessage = (error as Error).message;
      res.status(500).json({
        error: "An internal server error occurred.",
        details: errorMessage
      });
    }
  }

  private async continueConversation(req: Request, res: Response): Promise<void> {
    try {
      const conversationId = parseInt(req.params.id);
      const { text } = req.body;

      if (isNaN(conversationId)) {
        res.status(400).json({ error: 'Invalid conversation ID' });
        return;
      }

      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        res.status(400).json({
          error: 'Request body must include a non-empty "text" field'
        });
        return;
      }

      // Log user request
      logger.info(`[WEB] User request: "${text.trim()}" | Conversation: ${conversationId} | IP: ${req.ip}`);

      const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text.trim(), conversationId);
      const result = await handle.getResult();

      // Start polling for queued character thoughts if no immediate messages
      if (!result.reply || result.reply.length === 0) {
        logger.info(`No immediate messages in HTTP response, starting background polling for queued character thoughts`);
        this.startBackgroundPolling(conversationId);
      }

      res.json(result);
      
    } catch (error) {
      logger.error(`Error continuing conversation`, error);
      const errorMessage = (error as Error).message;

      if (errorMessage.includes('not found')) {
        res.status(404).json({ error: errorMessage });
      } else {
        res.status(500).json({
          error: "An internal server error occurred.",
          details: errorMessage
        });
      }
    }
  }

  private async getConversationHistory(req: Request, res: Response): Promise<void> {
    try {
      const conversationId = parseInt(req.params.id);

      if (isNaN(conversationId)) {
        res.status(400).json({ error: 'Invalid conversation ID' });
        return;
      }

      // Get conversation messages using DBOS workflow
      const handle = await DBOS.startWorkflow(ForaChat).getConversationMessages(conversationId);
      const messages = await handle.getResult();

      res.json({ conversationId, messages });

    } catch (error) {
      logger.error(`Error getting conversation history`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getConversationQueue(req: Request, res: Response): Promise<void> {
    try {
      const conversationId = parseInt(req.params.id);

      if (isNaN(conversationId)) {
        res.status(400).json({ error: 'Invalid conversation ID' });
        return;
      }

      // Get queued messages using DBOS workflow
      const handle = await DBOS.startWorkflow(ForaChat).getQueuedMessages(conversationId);
      const queuedMessages = await handle.getResult();

      res.json({ conversationId, queuedMessages });

    } catch (error) {
      logger.error(`Error getting conversation queue`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionInfo(req: Request, res: Response): Promise<void> {
    try {
      const session = (req as any).session;

      if (!session) {
        res.status(404).json({ error: 'No session found' });
        return;
      }

      res.json({
        sessionId: session.id,
        conversationId: session.conversation_id,
        channel: session.channel,
        createdAt: session.created_at,
        lastActivity: session.last_activity
      });
    } catch (error) {
      logger.error(`Error getting session info`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionConversations(req: Request, res: Response): Promise<void> {
    try {
      const session = (req as any).session;

      if (!session) {
        res.status(404).json({ error: 'No session found' });
        return;
      }

      if (!session.conversation_id) {
        res.json({ conversations: [] });
        return;
      }

      // Get conversation messages
      const handle = await DBOS.startWorkflow(ForaChat).getConversationMessages(session.conversation_id);
      const messages = await handle.getResult();

      res.json({
        conversationId: session.conversation_id,
        messages
      });
    } catch (error) {
      logger.error(`Error getting session conversations`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async createSessionAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionRequest: SessionCreateRequest = req.body;

      if (!sessionRequest.userIdentifier || !sessionRequest.channel) {
        res.status(400).json({
          error: 'userIdentifier and channel are required'
        });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
      const session = await handle.getResult();

      res.json({
        sessionId: session.id,
        conversationId: session.conversation_id,
        channel: session.channel,
        createdAt: session.created_at
      });
    } catch (error) {
      logger.error(`Error creating session`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionInfoAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
      const session = await handle.getResult();

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      res.json({
        sessionId: session.id,
        conversationId: session.conversation_id,
        channel: session.channel,
        createdAt: session.created_at,
        lastActivity: session.last_activity
      });
    } catch (error) {
      logger.error(`Error getting session info`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async deleteSessionAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      await DBOS.startWorkflow(ForaChat).deleteSession(sessionId);
      res.json({ message: 'Session deleted successfully' });
    } catch (error) {
      logger.error(`Error deleting session`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async extendSessionAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;
      const { hours = 24 } = req.body;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      await DBOS.startWorkflow(ForaChat).extendSession(sessionId, hours);
      res.json({ message: `Session extended by ${hours} hours` });
    } catch (error) {
      logger.error(`Error extending session`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionConversationAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).getConversationBySession(sessionId);
      const conversation = await handle.getResult();

      if (!conversation) {
        res.status(404).json({ error: 'No conversation found for this session' });
        return;
      }

      const messagesHandle = await DBOS.startWorkflow(ForaChat).getConversationMessages(conversation.id);
      const messages = await messagesHandle.getResult();

      res.json({
        sessionId,
        conversation,
        messages
      });
    } catch (error) {
      logger.error(`Error getting session conversation`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getUserSessionsAPI(req: Request, res: Response): Promise<void> {
    try {
      const userIdentifier = req.params.identifier;

      if (!userIdentifier) {
        res.status(400).json({ error: 'User identifier is required' });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).getActiveSessionsForUser(userIdentifier);
      const sessions = await handle.getResult();

      res.json({ userIdentifier, sessions });
    } catch (error) {
      logger.error(`Error getting user sessions`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async cleanupSessionsAPI(req: Request, res: Response): Promise<void> {
    try {
      const handle = await DBOS.startWorkflow(ForaChat).cleanupExpiredSessions();
      const deletedCount = await handle.getResult();

      res.json({
        message: `Cleaned up ${deletedCount} expired sessions`,
        deletedCount
      });
    } catch (error) {
      logger.error(`Error cleaning up sessions`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getCleanupStatsAPI(req: Request, res: Response): Promise<void> {
    try {
      const handle = await DBOS.startWorkflow(ForaChat).getCleanupStats();
      const stats = await handle.getResult();

      res.json(stats);
    } catch (error) {
      logger.error(`Error getting cleanup stats`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async manualCleanupAPI(req: Request, res: Response): Promise<void> {
    try {
      const options = req.body || {};

      const handle = await DBOS.startWorkflow(ForaChat).manualSessionCleanup(options);
      const result = await handle.getResult();

      res.json({
        message: 'Manual cleanup completed',
        ...result
      });
    } catch (error) {
      logger.error(`Error performing manual cleanup`, error);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async startBackgroundPolling(conversationId: number): Promise<void> {
    // Start a background process to poll for queued character thoughts
    // This runs independently and processes messages from the queue
    setTimeout(async () => {
      try {
        let pollCount = 0;
        const maxPolls = 60; // Poll for up to 10 minutes (60 * 10 seconds)

        const pollInterval = setInterval(async () => {
          try {
            pollCount++;

            // Get ready messages from the queue
            const readyMessages = await MessageQueueService.getReadyMessages(conversationId);

            if (readyMessages.length > 0) {
              logger.info(`Background polling found ${readyMessages.length} ready messages for conversation ${conversationId}`);

              // Process each message
              for (const message of readyMessages) {
                // Add to conversation history
                await ConversationService.addMessage(
                  message.character,
                  message.text,
                  conversationId
                );

                // Mark as sent
                await MessageQueueService.updateMessageStatus(message.id, 'SENT');

                logger.info(`Processed queued message from ${message.character}: ${message.text.substring(0, 50)}...`);
              }
            }

            // Stop polling after max attempts or if no more messages expected
            if (pollCount >= maxPolls) {
              clearInterval(pollInterval);
              logger.info(`Background polling stopped for conversation ${conversationId} after ${pollCount} attempts`);
            }
          } catch (error) {
            logger.error(`Error in background polling for conversation ${conversationId}:`, error);
          }
        }, 10000); // Poll every 10 seconds

      } catch (error) {
        logger.error(`Error starting background polling for conversation ${conversationId}:`, error);
      }
    }, 1000); // Start polling after 1 second delay
  }

  // MessageInterface implementation
  async sendMessage(message: string): Promise<void> {
    // For web interface, this would typically be handled by the client
    console.log(`Web interface would send: ${message}`);
  }

  async receiveMessage(): Promise<string> {
    // For web interface, messages come through HTTP requests
    throw new Error('receiveMessage not applicable for web interface');
  }

  formatResponse(response: ChatResponse): string {
    return JSON.stringify(response);
  }

  getApp(): express.Application {
    return this.app;
  }
}
